{"paths": {"/content/getResources": {"get": {"summary": "Get content resources with filters and pagination", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "resourceType", "required": false, "schema": {"type": "string"}, "description": "Filter by the type of resource (e.g., \"article\", \"page\")"}, {"in": "query", "name": "resourceTag", "required": false, "schema": {"type": "string"}, "description": "Filter by a specific tag associated with the resource"}, {"in": "query", "name": "relationType", "required": false, "schema": {"type": "string"}, "description": "Filter by relation type (e.g., \"parent\", \"child\")"}, {"in": "query", "name": "isAssigned", "required": false, "schema": {"type": "boolean"}, "description": "Whether the resource is assigned (true/false)"}, {"in": "query", "name": "search", "required": false, "schema": {"type": "string"}, "description": "Full-text search term"}, {"in": "query", "name": "status", "required": false, "schema": {"type": "string"}, "description": "Filter by workflow status (e.g., \"draft\", \"published\")"}, {"in": "query", "name": "page", "required": false, "schema": {"type": "integer", "default": 1}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "integer", "default": 100}, "description": "Number of items per page"}, {"in": "query", "name": "fetchType", "required": false, "schema": {"type": "string"}, "description": "Type of fetch operation (e.g., \"all\", \"assignedOnly\")"}, {"in": "query", "name": "roleId", "required": false, "schema": {"type": "string"}, "description": "Role ID to filter resources by user role"}, {"in": "query", "name": "apiCallType", "required": false, "schema": {"type": "string"}, "description": "Indicates the context of the API call (e.g., \"inline\", \"bulk\")"}], "responses": {"200": {"description": "Resources fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Resources fetched successfully"}, "data": {"type": "array", "description": "List of resource objects", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "e4f8c0a2-3b7a-4c5d-9e2f-1a2b3c4d5e6f"}, "title": {"type": "string", "example": "Getting Started with Shade CMS"}, "resourceType": {"type": "string", "example": "article"}, "status": {"type": "string", "example": "published"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["guide", "intro"]}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-19T06:00:00Z"}}}}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 100}, "total": {"type": "integer", "example": 42}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "/content/getResourceInfo/{resourceId}": {"get": {"summary": "Get detailed information for a single resource", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "resourceId", "required": true, "schema": {"type": "string"}, "description": "The ID of the resource to retrieve"}], "responses": {"200": {"description": "Resource info fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Success"}, "resourceInfo": {"type": "object", "properties": {"id": {"type": "string", "example": "cmanxcohj00topc4yeubbou2m"}, "titleEn": {"type": "string", "example": "Home Page"}, "titleAr": {"type": "string", "example": "الصفحة الرئيسية"}, "slug": {"type": "string", "example": "home"}, "status": {"type": "string", "example": "ACTIVE"}, "resourceType": {"type": "string", "example": "MAIN_PAGE"}, "resourceTag": {"type": "string", "example": "HOME"}, "relationType": {"type": "string", "example": "PARENT"}, "isAssigned": {"type": "boolean", "example": false}, "liveVersionId": {"type": "string", "example": "cmanxcohn00tqpc4y5bpbjw7w"}, "newVersionEditModeId": {"type": "string", "nullable": true, "example": null}, "scheduledVersionId": {"type": "string", "nullable": true, "example": null}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-14T12:36:14.455Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-05-14T12:36:14.461Z"}, "_count": {"type": "object", "properties": {"versions": {"type": "integer", "example": 1}}}, "roles": {"type": "array", "items": {"type": "string"}, "example": []}, "liveVersion": {"type": "object", "properties": {"versionNumber": {"type": "integer", "example": 1}}}, "newVersionEditMode": {"type": "object", "nullable": true, "example": null}, "verifiers": {"type": "array", "items": {"type": "object"}, "example": []}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Resource not found"}}}}}}}}}, "/content/getAssignedUsers/{resourceId}": {"get": {"summary": "Get users assigned to a specific resource", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "resourceId", "required": true, "schema": {"type": "string"}, "description": "The ID of the resource for which to retrieve assigned users"}], "responses": {"200": {"description": "Assigned users fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Success"}, "assignedUsers": {"type": "object", "properties": {"roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "9c3083b6-de55-4eb4-b901-4d7dc742c7b3"}, "resourceId": {"type": "string", "format": "uuid", "example": "cmanxcohj00topc4yeubbou2m"}, "userId": {"type": "string", "format": "uuid", "example": "cmaoux1f1000qpc0w5vlqhgjx"}, "role": {"type": "string", "example": "MANAGER"}, "status": {"type": "string", "example": "ACTIVE"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-19T10:22:43.787Z"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "cmaoux1f1000qpc0w5vlqhgjx"}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}}}}}}, "verifiers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "ca9d4344-b067-4383-b5c6-992c6bd6639d"}, "stage": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "ACTIVE"}, "resourceId": {"type": "string", "format": "uuid", "example": "cmanxcohj00topc4yeubbou2m"}, "userId": {"type": "string", "format": "uuid", "example": "cmaovteo80010pc0ww0eqt1w3"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-19T10:22:43.795Z"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "cmaovteo80010pc0ww0eqt1w3"}, "name": {"type": "string", "example": "<PERSON><PERSON>"}}}}}}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Resource not found"}}}}}}}}}, "/content/getEligibleUsers": {"get": {"summary": "Get users eligible based on role type and permission", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "roleType", "required": false, "schema": {"type": "string"}, "description": "Filter eligible users by role type (e.g., \"MANAGER, USER\")"}, {"in": "query", "name": "permission", "required": false, "schema": {"type": "string"}, "description": "Filter eligible users by a specific permission (e.g., \"publish_content\")"}], "responses": {"200": {"description": "Eligible users fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Success"}, "eligibleUsers": {"type": "array", "description": "List of eligible users matching the criteria", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "cmaov27ni000spc0w9oxjpdd6"}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "7878787878"}, "status": {"type": "string", "example": "ACTIVE"}, "roles": {"type": "array", "description": "Roles assigned to the user", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "Publisher"}, "type": {"type": "string", "example": "USER"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["PUBLISH"]}}}}}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "/content/assignUser": {"post": {"summary": "Assign users to a content resource", "tags": ["Content"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["resourceId"], "properties": {"resourceId": {"type": "string", "example": "cmaw7xsgh00tdnt4val4aae3e"}, "manager": {"type": "string", "example": "ManagerUserId"}, "editor": {"type": "string", "example": "EditorUserId"}, "verifiers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "cmaw7xsgh00tdnt4val4aae3e"}, "stage": {"type": "integer", "example": 1}}}}, "publisher": {"type": "string", "example": "PublisherUserId"}}}}}}, "responses": {"200": {"description": "Users assigned successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Users assigned successfully"}, "assignedUsers": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "cmaw7xsgh00tdnt4val4aae3e"}, "titleEn": {"type": "string", "example": "Home Page"}, "titleAr": {"type": "string", "example": "الصفحة الرئيسية"}, "slug": {"type": "string", "example": "home"}, "status": {"type": "string", "example": "ACTIVE"}, "resourceType": {"type": "string", "example": "MAIN_PAGE"}, "resourceTag": {"type": "string", "example": "HOME"}, "relationType": {"type": "string", "example": "PARENT"}, "isAssigned": {"type": "boolean", "example": true}, "liveVersionId": {"type": "string", "format": "uuid", "example": "cmawb9jm30001nt9wne52ghzc"}, "newVersionEditModeId": {"type": "string", "format": "uuid", "example": "cmaw88umy000ant7zeqovh5w6"}, "scheduledVersionId": {"type": ["string", "null"], "nullable": true}, "parentId": {"type": ["string", "null"], "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-20T07:54:44.946Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-05-21T05:28:59.779Z"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resourceId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "role": {"type": "string"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "image": {"type": "string"}, "email": {"type": "string", "format": "email"}}}}}}, "verifiers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "stage": {"type": "integer"}, "status": {"type": "string"}, "resourceId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}}, "newVersionEditMode": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "versionNumber": {"type": "integer"}, "versionStatus": {"type": "string"}, "notes": {"type": "string"}, "referenceDoc": {"type": ["string", "null"], "nullable": true}, "content": {"type": "object"}, "icon": {"type": ["string", "null"], "nullable": true}, "Image": {"type": ["string", "null"], "nullable": true}, "lockedById": {"type": ["string", "null"], "nullable": true}, "lockedAt": {"type": ["string", "null"], "format": "date-time", "nullable": true}, "resourceId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "scheduledAt": {"type": ["string", "null"], "format": "date-time", "nullable": true}, "publishedAt": {"type": ["string", "null"], "format": "date-time", "nullable": true}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "role": {"type": "string"}, "status": {"type": "string"}, "resourceVersionId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}}, "verifiers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "stage": {"type": "integer"}, "status": {"type": "string"}, "resourceVersionId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}}}}}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}}}, "/content/removeAssignedUser/{resourceId}": {"patch": {"summary": "Remove an assigned user from a specific resource", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "resourceId", "required": true, "schema": {"type": "string"}, "description": "The ID of the resource from which to remove the user"}], "responses": {"200": {"description": "Users removed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Users removed successfully"}, "result": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "All active user assignments for resource cmanxcohj00topc4yeubbou2m have been marked as inactive"}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}, "404": {"description": "Resource or assigned user not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Assigned user not found for this resource"}}}}}}}}}, "/content/getContent/{resourceId}": {"get": {"summary": "Get the raw content for a specific resource", "tags": ["Content"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "resourceId", "required": true, "schema": {"type": "string"}, "description": "The ID of the resource to retrieve content for"}], "responses": {"200": {"description": "Resource info fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Success"}, "content": {"type": "object", "properties": {"id": {"type": "string", "example": "cmanxcohj00topc4yeubbou2m"}, "titleEn": {"type": "string", "example": "Home Page"}, "titleAr": {"type": "string", "example": "الصفحة الرئيسية"}, "slug": {"type": "string", "example": "home"}, "resourceType": {"type": "string", "example": "MAIN_PAGE"}, "resourceTag": {"type": "string", "example": "HOME"}, "relationType": {"type": "string", "example": "PARENT"}, "liveModeVersionData": {"type": "object", "properties": {"id": {"type": "string", "example": "cmanxcohn00tqpc4y5bpbjw7w"}, "versionNumber": {"type": "integer", "example": 1}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "comments": {"type": "string", "example": "Initial version created"}, "referenceDoc": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-05-14T12:36:14.459Z"}, "status": {"type": "string", "example": "PUBLISHED"}, "sections": {"type": "array", "items": {"type": "object", "properties": {"sectionId": {"type": "string", "example": "cmanxcohz00tspc4yn7rl5kk6"}, "order": {"type": "integer", "example": 1}, "version": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "HeroSection-home-b3dd"}, "content": {"type": "object", "properties": {"title": {"type": "object", "properties": {"ar": {"type": "string", "example": "بناء مستقبل أقوى"}, "en": {"type": "string", "example": "Building a Stronger Future"}}}, "button": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "text": {"type": "object", "properties": {"ar": {"type": "string", "example": "أعمالنا"}, "en": {"type": "string", "example": "View Our Work"}}}, "order": {"type": "integer", "example": 1}}}}, "images": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string"}, "order": {"type": "integer", "example": 1}, "altText": {"type": "object", "properties": {"ar": {"type": "string"}, "en": {"type": "string"}}}}}}, "description": {"type": "object", "properties": {"ar": {"type": "string"}, "en": {"type": "string"}}}}}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "titleEn": {"type": "string"}, "titleAr": {"type": "string"}, "slug": {"type": "string"}}}}}}}}}}}}}}}}, "400": {"$ref": "#/components/schemas/ErrorResponse"}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Resource not found"}}}}}}}}}, "/content/updateContent": {"put": {"summary": "Update or save content for resources", "tags": ["Content"], "security": [{"BearerAuth": []}], "requestBody": {"description": "Full content payload, including draft mode and structured sections", "required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["resourceId", "titleEn", "titleAr", "slug", "newVersionEditMode", "sections"], "properties": {"resourceId": {"type": "string", "format": "uuid", "example": "cmanwipvc00toqrj6zytmop4d"}, "titleEn": {"type": "string", "example": "Home Page"}, "titleAr": {"type": "string", "example": "الصفحة الرئيسية"}, "slug": {"type": "string", "example": "home"}, "newVersionEditMode": {"type": "object", "required": ["comments", "sections"], "properties": {"comments": {"type": "string", "example": "Initial version created"}, "referenceDoc": {"type": "string", "nullable": true, "example": null}, "icon": {"type": "string", "nullable": true, "example": null}, "image": {"type": "string", "nullable": true, "example": null}, "sections": {"type": "array", "items": {"type": "object", "required": ["sectionId", "order", "content"], "properties": {"sectionId": {"type": "string", "format": "uuid", "example": "cmanwipvr00tsqrj6nkmm7080"}, "order": {"type": "integer", "example": 1}, "content": {"type": "object", "properties": {"title": {"type": "object", "properties": {"ar": {"type": "string", "example": "بناء مستقبل أقوى"}, "en": {"type": "string", "example": "Building a Stronger Future"}}}, "button": {"type": "array", "items": {"type": "object", "required": ["order", "text"], "properties": {"url": {"type": "string", "nullable": true, "example": null}, "icon": {"type": "string", "nullable": true, "example": null}, "text": {"type": "object", "properties": {"ar": {"type": "string", "example": "أعمالنا"}, "en": {"type": "string", "example": "View Our Work"}}}, "order": {"type": "integer", "example": 1}}}}, "images": {"type": "array", "items": {"type": "object", "required": ["order", "altText"], "properties": {"url": {"type": "string", "example": ""}, "order": {"type": "integer", "example": 1}, "altText": {"type": "object", "properties": {"ar": {"type": "string", "example": "الرئيسية"}, "en": {"type": "string", "example": "Image"}}}}}}, "description": {"type": "object", "properties": {"ar": {"type": "string", "example": "التزامنا الثابت الذي يعزز الشراكات..."}, "en": {"type": "string", "example": "Our unwavering commitment that forge..."}}}}}, "items": {"type": "array", "items": {"type": "object", "required": ["order", "id"], "properties": {"order": {"type": "integer", "example": 1}, "id": {"type": "string", "format": "uuid", "example": "cmanwipcm00fiqrj6c05xml7u"}}}}}}}}}, "sections": {"type": "array", "items": {"type": "object", "required": ["sectionId", "order", "content"], "properties": {"sectionId": {"type": "string", "format": "uuid", "example": "cmanwipwr00ukqrj6r5gd4v50"}, "order": {"type": "integer", "example": 4}, "content": {"type": "object", "properties": {"cards": {"type": "array", "items": {"type": "object", "properties": {"icon": {"type": "string", "example": ""}, "count": {"type": "string", "example": "123"}, "order": {"type": "integer", "example": 1}, "title": {"type": "object", "properties": {"ar": {"type": "string", "example": "المشاريع المنجزة"}, "en": {"type": "string", "example": "Projects Completed"}}}}}}, "button": {"type": "array", "items": {"type": "object", "required": ["order", "text"], "properties": {"url": {"type": "string", "nullable": true, "example": null}, "icon": {"type": "string", "nullable": true, "example": null}, "text": {"type": "object", "properties": {"ar": {"type": "string", "example": "اتصل بنا"}, "en": {"type": "string", "example": "Contact Us"}}}, "order": {"type": "integer", "example": 1}}}}, "description": {"type": "object", "properties": {"ar": {"type": "string", "example": "كانت شركتنا رائدة..."}, "en": {"type": "string", "example": "Our company has been the leading..."}}}, "buttons": {"type": "array", "items": {"type": "object", "required": ["order", "text"], "properties": {"icon": {"type": "string", "nullable": true}, "text": {"type": "object", "properties": {"ar": {"type": "string", "example": "عر<PERSON> الكل"}, "en": {"type": "string", "example": "View All"}}}, "order": {"type": "integer", "example": 1}}}}, "sections": {"type": "array", "items": {"type": "object", "required": ["sectionId", "order", "content"], "properties": {"sectionId": {"type": "string", "format": "uuid", "example": "cmanwipx600uwqrj647aqzfw5"}, "order": {"type": "integer", "example": 1}, "content": {"type": "object", "properties": {"id": {"type": "string", "example": "projectsSlugs"}, "title": {"type": "object", "properties": {"ar": {"type": "string", "example": "المشاريع الأخيرة"}, "en": {"type": "string", "example": "Recent Projects"}}}, "description": {"type": "object", "properties": {"ar": {"type": "string", "example": "تفتخر شركة شيد..."}, "en": {"type": "string", "example": "Shade Corporation boasts..."}}}}}}}}}}, "items": {"type": "array", "items": {"type": "object", "properties": {"order": {"type": "integer", "example": 1}, "id": {"type": "string", "format": "uuid", "example": "cmanwiox7004kqrj6excs2vyo"}}}}}}}}}}}}, "responses": {"200": {"description": "Resource version updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Resource version updated successfully"}, "resource": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "cmaw7xsgh00tdnt4val4aae3e"}, "titleEn": {"type": "string", "example": "Home Page"}, "titleAr": {"type": "string", "example": "الصفحة الرئيسية"}, "slug": {"type": "string", "example": "home"}, "status": {"type": "string", "example": "ACTIVE"}, "resourceType": {"type": "string", "example": "MAIN_PAGE"}, "resourceTag": {"type": "string", "example": "HOME"}, "relationType": {"type": "string", "example": "PARENT"}, "isAssigned": {"type": "boolean", "example": true}, "liveVersionId": {"type": "string", "format": "uuid", "example": "cmaw7xsgk00tfnt4vxpnwe62i"}, "newVersionEditModeId": {"type": "string", "format": "uuid", "example": "cmaw88umy000ant7zeqovh5w6"}, "scheduledVersionId": {"type": "string", "format": "uuid", "nullable": true, "example": null}, "parentId": {"type": "string", "format": "uuid", "nullable": true, "example": null}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-22T12:00:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-05-22T12:15:00Z"}}}}}}}}, "400": {"description": "Bad Request - Validation error or missing fields", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload"}}}}}}, "401": {"description": "Unauthorized - Missing or invalid authentication", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Authentication required"}}}}}}, "404": {"description": "Resource not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Resource not found"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Server error"}}}}}}}}}}}