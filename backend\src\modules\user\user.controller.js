import {getSocketId} from "../../helper/socketConnectionID.js";
import {
  activateUsers,
  AssignPageRole,
  assignRole,
  createUser,
  deactivateUsers,
  editProfile,
  editProfileImage,
  editUserDetails,
  getAllLocations,
  getAllRolesForUser,
  getAllUsers,
  getAllUsersByRoleId,
  getUserById,
  removeRole,
  userRoleType,
} from "./user.service.js";
import {handleEntityCreationNotification} from "../../helper/notificationHelper.js";
import {assignPageRole} from "../../repository/user.repository.js";

const CreateUserHandler = async (req, res) => {
  const {name, email, password, phone, locationId} = req.body;
  const user = await createUser(name, email, password, phone, locationId);
  console.log(user, "--------------------------------user");
  res.locals.entityId = user?.user?.id;
  res.status(201).json(user);
  // Notification: user created
  // const io = req.app.locals.io;
  // await handleEntityCreationNotification({
  //   io,
  //   userId: req.user?.id || user.user.id, // fallback to created user if no actor
  //   entity: "user",
  //   newValue: user.user,
  //   actionType: "CREATE",
  //   targetUserId: user.user.id,
  // });
};

const AssignPageRoleHandler = async (req, res) => {
  const {userId, webpageId, roleId} = req.body;

  // Only super admins can assign roles - enforced by middleware
  const assignment = await AssignPageRole(userId, webpageId, roleId);

  res.status(201).json({
    message: "Role assigned successfully",
    assignment,
  });
};

const GetAllUsers = async (req, res) => {
  const {name, email, phone, status, page, limit} = req.query;
  const pageNum = parseInt(page) || 1;
  const limitNum = parseInt(limit) || 40;
  const allUser = await getAllUsers(
    name,
    email,
    phone,
    status,
    pageNum,
    limitNum
  );
  res.status(201).json(allUser);
};

const GetUserById = async (req, res) => {
  const {id} = req.params;
  const response = await getUserById(id);
  res.status(200).json(response);
};

const GetUserProfile = async (req, res) => {
  const {id} = req.user;
  const user = await getUserById(id);
  res.status(200).json(user);
};

const GetAllUsersByRoleId = async (req, res) => {
  const {roleId} = req.params;
  const allUser = await getAllUsersByRoleId(roleId);
  res.status(201).json(allUser);
};

const EditUserDetails = async (req, res) => {
  const {id} = req.params;
  const {name, password, phone, locationId} = req.body;
  const updatedUser = await editUserDetails(
    id,
    name,
    password,
    phone,
    locationId
  );
  // const io = req.app.locals.io;
  // Notification: user updated
  // await handleEntityCreationNotification({
  //   io,
  //   userId: req.user?.id,
  //   entity: "user",
  //   newValue: updatedUser.result,
  //   actionType: "UPDATE",
  //   targetUserId: id,
  // });
  // const socketIdOfUpdatedUser = getSocketId(id);
  // io.to(socketIdOfUpdatedUser).emit("userUpdated", updatedUser);
  res.status(201).json(updatedUser);
};

const EditProfile = async (req, res) => {
  const {id} = req.user;
  const {name, phone, image} = req.body;
  const updatedUser = await editProfile(id, name, phone, image);
  res.status(201).json(updatedUser);
};

const ActivateUser = async (req, res) => {
  const {id} = req.body;
  const result = await activateUsers(id);
  res.status(200).json(result);
};

const DeactivateUser = async (req, res) => {
  const {id} = req.body;
  const result = await deactivateUsers(id);
  res.locals.entityId = id;
  // const io = req.app.locals.io;
  // const socketIdOfUpdatedUser = getSocketId(id);
  // io.to(socketIdOfUpdatedUser).emit("userUpdated", {result});
  res.status(200).json(result);
};

const GetRolesForUser = async (req, res) => {
  const result = await getAllRolesForUser();
  res.status(200).json(result);
};

const UserRoleType = async (req, res) => {
  const {id} = req.params;
  const result = await userRoleType(id);
  res.status(200).json(result);
};

const EditProfileImage = async (req, res) => {
  const {id} = req.user;
  if (!req.uploadedImages || req.uploadedImages.length === 0) {
    return res.status(400).json({error: "No uploadedImages found"});
  }
  const {public_id: imageUrl} = req.uploadedImages[0];
  if (!imageUrl) {
    return res.status(400).json({error: "Cloudinary did not return a URL"});
  }
  const updatedUser = await editProfileImage(id, imageUrl);
  res.status(201).json(updatedUser);
};

const GetAllLocations = async (req, res) => {
  const location = await getAllLocations();
  res.status(201).json(location);
};

const AssignRoleToWebpage = async (req, res) => {
  try {
    const {webpageId, userId, roleId} = req.body;

    if (!userId || !roleId) {
      return res.status(400).json({
        success: false,
        message: "userId and roleId are required",
      });
    }

    const result = await assignRole(webpageId, userId, roleId);

    res.status(200).json({
      success: true,
      message: `User successfully assigned to webpage role`,
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    });
  }
};

// controllers/UserController.ts
const RemoveRoleFromWebpage = async (req, res) => {
  try {
    const {webpageId, roleId} = req.body;

    if (!webpageId || !roleId) {
      return res.status(400).json({
        success: false,
        message: "webpageId and roleId are required",
      });
    }

    const result = await removeRole(webpageId, roleId);

    res.status(200).json({
      success: true,
      message: `Role successfully removed from webpage`,
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    });
  }
};

export default {
  CreateUserHandler,
  AssignPageRoleHandler,
  GetAllUsers,
  GetUserById,
  EditUserDetails,
  EditProfile,
  ActivateUser,
  DeactivateUser,
  UserRoleType,
  GetRolesForUser,
  GetAllUsersByRoleId,
  GetUserProfile,
  EditProfileImage,
  GetAllLocations,
  AssignRoleToWebpage,
  RemoveRoleFromWebpage,
};
