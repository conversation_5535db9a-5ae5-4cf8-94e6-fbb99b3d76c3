// model UserRole {
//   userId    String
//   roleId    String
//   // user      User     @relation(fields: [userId], references: [id])
//   // role      Role     @relation(fields: [roleId], references: [id])
//   createdAt DateTime @default(now())

//   @@id([userId, roleId]) // primary key as combination of both id
//   @@index([userId])
//   @@index([roleId])
// }

model UserAuditLog {
  auditLogId String   @unique
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  auditLog   AuditLog @relation(fields: [auditLogId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())

  @@id([auditLogId, userId]) // primary key as combination of both id
  @@index([auditLogId])
  @@index([userId])
}

// model RolePermission {
//   roleId       String
//   permissionId String
//   // role         Role       @relation(fields: [roleId], references: [id])
//   permission   Permission @relation(fields: [permissionId], references: [id])
//   createdAt    DateTime   @default(now())

//   @@id([roleId, permissionId]) // primary key as combination of both id
//   @@index([roleId])
//   @@index([permissionId])
// }

// model PermissionSubPermission {
//   permissionId    String
//   subPermissionId String
//   permission      Permission    @relation(fields: [permissionId], references: [id])
//   subPermission   SubPermission @relation(fields: [subPermissionId], references: [id])
//   createdAt       DateTime      @default(now())

//   @@id([permissionId, subPermissionId]) // primary key as combination of both id
//   @@index([permissionId])
//   @@index([subPermissionId])
// }

// Junction table linking resource versions to section versions
model ResourceVersionSection {
  id    String @id @default(cuid())
  order Int // Display sequence

  // Relationships with cascading deletes
  // resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId String
  // sectionVersion    SectionVersion  @relation(fields: [sectionVersionId], references: [id], onDelete: Cascade)
  sectionVersionId  String

  // Composite constraints
  @@unique([resourceVersionId, sectionVersionId]) // Prevent duplicate sections
  @@index([resourceVersionId, order]) // For ordered retrieval
  @@index([resourceVersionId])
  @@index([sectionVersionId])
}

// Content items within sections (e.g., cards in a grid)
// model SectionVersionItem {
//   id    String @id @default(cuid())
//   order Int // Display sequence

//   // Relationships with cascading deletes
//   sectionVersion   SectionVersion @relation(fields: [sectionVersionId], references: [id], onDelete: Cascade)
//   sectionVersionId String
//   resource         Resource       @relation(fields: [resourceId], references: [id], onDelete: Cascade)
//   resourceId       String

//   // Composite constraints
//   @@unique([sectionVersionId, resourceId]) // Prevent duplicates
//   @@index([sectionVersionId, order]) // For ordered retrieval
//   @@index([sectionVersionId])
//   @@index([resourceId])
// }

// TO TRACK USER ROLES(EDITOR, PUBLISHER, MANAGER) FOR THIS RESOURCE
// ROLES CAN BE CHANGED AT ANY TIME
// model ResourceRole {
//   id         String           @id @default(uuid())
//   resource   Resource         @relation(fields: [resourceId], references: [id])
//   resourceId String
//   user       User             @relation(fields: [userId], references: [id])
//   userId     String
//   role       ResourceRoleType
//   status     Status           @default(ACTIVE) // Add status field
//   createdAt  DateTime         @default(now())

//   @@unique([resourceId, role, userId, status]) // ✅ No duplicate user-role-status entry
//   @@index([resourceId])
//   @@index([userId])
// }

// STORE HISTORY OF ROLES FOR THIS RESOURCE VERSION
// CAN NOT CHANGED AS THIS IS JUST TO STORE THE HISTORY
// Historical permission snapshot for versions
// model ResourceVersionRole {
//   id     String           @id @default(uuid())
//   role   ResourceRoleType // Permission level at version creation
//   status Status           @default(ACTIVE) // Add status field

//   // Relationships
//   resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
//   resourceVersionId String
//   user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
//   userId            String

//   // Timestamps
//   createdAt DateTime @default(now())

//   // Composite constraints
//   @@unique([resourceVersionId, role, userId, status]) // ✅ No duplicate user-role-status in version 
//   @@index([resourceVersionId]) // For version context
//   @@index([userId])
// }

// TO TRACK VERIFIERS(MANY, ONE USER PER STAGE) FOR THIS RESOURCE
// VERIFIERS CAN BE CHANGED AT ANY TIME
// Current approval workflow configuration
// model ResourceVerifier {
//   id     String @id @default(uuid())
//   stage  Int
//   status Status @default(ACTIVE)

//   resource   Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
//   resourceId String
//   user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
//   userId     String

//   createdAt DateTime @default(now())

//   // Composite constraints
//   @@unique([resourceId, stage, userId, status]) // ✅ Prevent duplicate verifier assignments 
//   // Indexes
//   @@index([resourceId, stage])
//   @@index([userId])
//   @@index([resourceId])
// }

// TO TRACK VERIFIERS(MANY, ONE USER PER STAGE) FOR THIS RESOURCE VERSION
// CAN NOT CHANGED AS THIS IS JUST TO STORE THE HISTORY
// Historical approval snapshot for versions
// model ResourceVersionVerifier {
//   id     String @id @default(uuid())
//   stage  Int
//   status Status @default(ACTIVE)

//   resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
//   resourceVersionId String
//   user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
//   userId            String

//   createdAt DateTime @default(now())

//   // Composite constraints
//   @@unique([resourceVersionId, stage, userId, status]) // ✅ Prevent duplicate verifier entries 
//   // Indexes
//   @@index([resourceVersionId, stage])
//   @@index([resourceVersionId])
//   @@index([userId])
// }

// Content workflow requests
// model ResourceVersioningRequest {
//   id                String            @id @default(uuid())
//   status            RequestStatus     @default(VERIFICATION_PENDING)
//   flowStatus        FlowStatus        @default(PENDING)
//   type              RequestType // VERIFICATION or PUBLICATION
//   editorComments    String? // Context for reviewers
//   // Relationships
//   // resourceVersion   ResourceVersion   @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
//   resourceVersionId String
//   sender            User              @relation("sender", fields: [senderId], references: [id], onDelete: Cascade)
//   senderId          String
//   approvals         RequestApproval[] // Approval records

//   // Timestamps
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([resourceVersionId])
//   @@index([senderId])
//   // Indexes
//   @@index([resourceVersionId, status]) // For request tracking
//   @@index([status, type]) // For workflow processing
// }

// Individual approval actions
// model RequestApproval {
//   id             String                    @id @default(uuid())
//   stage          Int? // Verification stage (null for publisher)
//   status         ApprovalStatus            @default(PENDING)
//   comments       String? // Feedback from approver
//   // Relationships
//   request        ResourceVersioningRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
//   requestId      String
//   approver       User                      @relation("approver", fields: [approverId], references: [id], onDelete: Cascade)
//   approverId     String
//   approverStatus Status                    @default(ACTIVE)

//   // Timestamps
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   // Composite constraints
//   @@unique([requestId, approverId, stage]) // Prevent duplicate approvals for same stage/role
//   @@unique([requestId, approverId, approverStatus, stage]) // Ensures only one active role per request per user
//   // Indexes
//   @@index([requestId, status]) // For request progress
//   @@index([approverId]) // For user dashboard
//   @@index([requestId])
// }

// State transition rules for workflow validation
// model WorkflowState {
//   id          String           @id @default(cuid())
//   fromState   VersionStatus
//   toState     VersionStatus
//   allowedRole ResourceRoleType // Changed from relation to direct enum reference

//   // Timestamps
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   // Corrected unique index - only scalar fields
//   @@unique([fromState, toState, allowedRole])
//   @@index([fromState])
// }

model PageUserRole {
  userId    String
  webpageId String
  roleId    String
  user      User     @relation(fields: [userId], references: [id])
  webpage   Webpage  @relation(fields: [webpageId], references: [id])
  role      Role     @relation(fields: [roleId], references: [id])
  createdAt DateTime @default(now())

  @@id([userId, webpageId, roleId]) // A user can have one role per webpage
  @@unique([webpageId, roleId]) // Ensures one editor and one verifier per page
  @@index([userId])
  @@index([webpageId])
  @@index([roleId])
}
