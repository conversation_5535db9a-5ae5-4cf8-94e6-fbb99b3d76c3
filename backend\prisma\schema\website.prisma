model Webpage {
  id          String         @id @default(uuid())
  name        String
  route       String         @unique
  locationId  String?
  location    location?      @relation(fields: [locationId], references: [id])
  contents    Content[]
  versions    version[]
  pageRoles   PageUserRole[]
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  editedWidth String?
  Status      Boolean        @default(true)

  editorId String?
  editor   User?   @relation("WebpageEditor", fields: [editorId], references: [id])

  verifierId      String?
  verifier        User?             @relation("WebpageVerifier", fields: [verifierId], references: [id])
  proposedVersion proposedVersion[]
  draft           draft[]

  @@index([locationId])
  @@index([editorId])
  @@index([verifierId])
} 

model Content {
  id        String  @id @default(uuid())
  name      String
  givenName String?
  order     Int     @default(0)
  hover     Json?
  aria      String?

  // For top-level sections
  webpageId String?
  webpage   Webpage? @relation(fields: [webpageId], references: [id])

  // Self-relation
  parentId String?
  parent   Content?  @relation("SectionChildren", fields: [parentId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  children Content[] @relation("SectionChildren")

  elements Element[]
  style    Style     @relation("ContentStyle", fields: [styleId], references: [id], onDelete: Cascade)
  styleId  String    @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([webpageId])
  @@index([parentId])
}

model Element {
  id      String   @id @default(uuid())
  name    String
  content String
  items   String[] @default([]) // for list
  order   Int      @default(0)

  contentId  String
  contentRef Content @relation(fields: [contentId], references: [id], onDelete: Cascade)

  style   Style  @relation("ElementStyle", fields: [styleId], references: [id], onDelete: Cascade)
  styleId String @unique

  hover Json?
  href  String?
  aria  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([contentId])
}

model Style {
  id      String   @id @default(uuid())
  xl      Json?
  lg      Json?
  md      Json?
  sm      Json?
  content Content? @relation("ContentStyle")
  element Element? @relation("ElementStyle")
}

model version {
  id        String  @id @default(uuid())
  version   Json
  webpageId String
  webpage   Webpage @relation(fields: [webpageId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([webpageId])
}

model proposedVersion {
  id        String  @id @default(uuid())
  version   Json
  webpageId String
  webpage   Webpage @relation(fields: [webpageId], references: [id])
  editorId  String
  editor    User    @relation("ProposedVersionEditor", fields: [editorId], references: [id])

  verifierId String
  verifier   User   @relation("ProposedVersionVerifier", fields: [verifierId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([webpageId])
  @@index([editorId])
  @@index([verifierId])
}

model draft {
  id        String  @id @default(uuid())
  version   Json
  webpageId String
  webpage   Webpage @relation(fields: [webpageId], references: [id])
  userId    String
  user      User    @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([webpageId])
  @@index([userId])
}

// A user can be an editor on one page and a verifier on another.
// A webpage has exactly one editor and one verifier at a time.
// A user cannot be both the editor and verifier of the same webpage.
// The concept of Role (like "Editor", "Verifier") still exists but isn't globally assigned to users.
