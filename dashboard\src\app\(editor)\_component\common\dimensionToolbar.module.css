.colorInput {
  width: 32px;
  height: 32px;
  border: 1px solid black;
  cursor: pointer;
  background: none;
  padding: 0;
  /* clip-path: circle(); */
  border-radius: 50%;
  /* 👈 adds clean circular shape */
  -webkit-appearance: none;
  /* fix for some browsers */
  appearance: none;
  box-shadow: 0 0 0 1px #ccc;
}

.colorInput::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
}

.colorInput::-webkit-color-swatch {
  border: none;
  border-radius: 50%;
}

/* Prevent hex input cut-off */
.input {
  padding: 4px 8px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-width: 0;
  max-width: 100%;
  flex: 1 1 auto;
  /* 👈 allow input to flex but not overflow */
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


/* Fix horizontal scroll */
.toolbar {
  position: fixed;
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  /* z-index: 50; */
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  transition: top 0.1s ease-out, left 0.1s ease-out;
  max-width: calc(100vw - 20px);
  max-height: calc(100vh - 20px);
  overflow-y: auto;
  overflow-x: hidden;
  /* ← this disables horizontal scroll */
  user-select: none;
  width: clamp(320px, 30vw, 420px);
}


.dragHandle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* cursor: grab; */
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #d1d5db;
  margin-bottom: 0.5rem;
  touch-action: none;
}

.heading {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.sectionLabel {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  margin-top: 1rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 0.75rem;
}

.row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
  flex-wrap: wrap;
}

.label {
  min-width: 130px;
  font-weight: 500;
  color: #374151;
}

.inputGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.input {
  padding: 4px 8px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-width: 120px;
  outline: none;
  transition: box-shadow 0.2s ease-in-out;
}

.input:focus {
  box-shadow: 0 0 0 2px #3b82f6;
}

.button {
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #0056b3;
}

.colorPair {
  display: flex;
  gap: 12px;
  align-items: center;
}

.colorItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.colorLabel {
  font-size: 12px;
  font-weight: 500;
}

.colorInput {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}
