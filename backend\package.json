{"name": "shade_server", "version": "*******", "main": "src/server.js", "type": "module", "scripts": {"start": "nodemon --legacy-watch server.js", "start:local": "dotenv -e ../.env nodemon --legacy-watch server.js", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "prisma:migrate": "npx prisma migrate dev", "prisma:generate": "npx prisma generate", "seed": "node prisma/seed.js"}, "author": "akshykmr", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.2.1", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cloudinary": "^2.6.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "http-errors-lite": "^2.0.2", "http-status-codes": "^2.3.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "pg": "^8.13.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22.10.2", "prisma": "^6.2.1"}}