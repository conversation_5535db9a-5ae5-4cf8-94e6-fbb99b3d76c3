{"name": "fronted_next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "lodash": "^4.17.21", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "sonner": "^2.0.7", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19.1.11", "@types/react-dom": "^19", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.4.5", "framer-motion": "^12.23.12", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5"}}