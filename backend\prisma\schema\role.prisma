// model RoleType {
//     id          String       @id @default(uuid())
//     name        String       @unique // USER or MANAGER
//     roles       Role[]
//     permissions Permission[]
// }

model Role {
  id            String         @id @default(cuid())
  name          String         @unique // 'Editor', 'Verifier'
  status        Status         @default(ACTIVE)
  pageUserRoles PageUserRole[]
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt

  @@index([name])
}
