export const preSection = {
    "id": "",
    "route": "",
    "locationId": "",
    "name": "",
    "contents": [
        {
            "name": "section",
            "elements": [
                {
                    "name": "section",
                    "elements": [
                        {
                            "style": {
                                "xl": {
                                    "fontWeight": "bold",
                                    "fontSize": "40px"
                                },
                                "lg": {
                                    "fontWeight": "bold",
                                    "fontSize": "40px"
                                },
                                "md": {
                                    "fontWeight": "bold",
                                    "fontSize": "30px"
                                },
                                "sm": {
                                    "fontWeight": "bold",
                                    "fontSize": "26px"
                                }
                            },
                            "name": "h1",
                            "content": "Welcome to your page editor!",
                            "id": crypto.randomUUID()
                        },
                        {
                            "style": {
                                "xl": {
                                    "textAlign": "center"
                                },
                                "lg": {
                                    "textAlign": "center"
                                },
                                "md": {
                                    "textAlign": ""
                                },
                                "sm": {
                                    "textAlign": ""
                                }
                            },
                            "name": "p",
                            "content": "Here you can easily create and customize web pages without needing any code.&nbsp;<div>Start by setting your page name and route, then add sections for text, images, or other content.&nbsp;</div><div>Use the sidebar tools to style and adjust your layout, and switch between&nbsp;</div><div>desktop, tablet, and mobile views to see how your page looks on different devices.&nbsp;</div><div>When you're happy with your design, just click <strong data-start=\"521\" data-end=\"537\">Save Changes</strong> and your updates will be ready.</div>",
                            "id": crypto.randomUUID()
                        }
                    ],
                    "style": {
                        "xl": {
                            "width": "100%",
                            "minWidth": "2px",
                            "minHeight": "1vh",
                            "height": "60vh",
                            "paddingTop": 20,
                            "paddingRight": "20px",
                            "paddingBottom": "20px",
                            "paddingLeft": "20px",
                            "marginLeft": 0,
                            "marginRight": 0,
                            "marginTop": 0,
                            "marginBottom": 0,
                            "position": "relative",
                            "top": "0",
                            "left": "0",
                            "overflow": "hidden",
                            "backgroundImage": "linear-gradient(to right, rgba(127,144,148,1), rgba(145,191,202,1))",
                            "display": "flex",
                            "flexDirection": "column",
                            "alignItems": "center",
                            "justifyContent": "center",
                            "color": "#FFFFFF",
                            "borderRadius": 50,
                            "boxShadow": "0 10px 15px rgba(0,0,0,0.3)"
                        },
                        "lg": {
                            "width": "100%",
                            "minWidth": "2px",
                            "minHeight": "1vh",
                            "height": "60vh",
                            "paddingTop": 20,
                            "paddingRight": "20px",
                            "paddingBottom": "20px",
                            "paddingLeft": "20px",
                            "marginLeft": 0,
                            "marginRight": 0,
                            "marginTop": 0,
                            "marginBottom": 0,
                            "position": "relative",
                            "top": "0",
                            "left": "0",
                            "overflow": "hidden",
                            "backgroundImage": "linear-gradient(to right, rgba(127,144,148,1), rgba(145,191,202,1))",
                            "display": "flex",
                            "flexDirection": "column",
                            "alignItems": "center",
                            "justifyContent": "center",
                            "color": "#FFFFFF",
                            "borderRadius": 50,
                            "boxShadow": "0 10px 15px rgba(0,0,0,0.3)"
                        },
                        "md": {
                            "width": "100%",
                            "minWidth": "2px",
                            "minHeight": "1vh",
                            "height": "60vh",
                            "paddingTop": 20,
                            "paddingRight": "20px",
                            "paddingBottom": "20px",
                            "paddingLeft": "20px",
                            "marginLeft": 0,
                            "marginRight": 0,
                            "marginTop": 0,
                            "marginBottom": 0,
                            "position": "relative",
                            "top": "0",
                            "left": "0",
                            "overflow": "hidden",
                            "backgroundImage": "linear-gradient(to right, rgba(127,144,148,1), rgba(145,191,202,1))",
                            "display": "flex",
                            "flexDirection": "column",
                            "alignItems": "center",
                            "justifyContent": "center",
                            "color": "#FFFFFF",
                            "borderRadius": 50,
                            "boxShadow": "0 10px 15px rgba(0,0,0,0.3)"
                        },
                        "sm": {
                            "width": "100%",
                            "minWidth": "2px",
                            "minHeight": "1vh",
                            "height": "70vh",
                            "paddingTop": 20,
                            "paddingRight": "20px",
                            "paddingBottom": "20px",
                            "paddingLeft": "20px",
                            "marginLeft": 0,
                            "marginRight": 0,
                            "marginTop": 0,
                            "marginBottom": 0,
                            "position": "relative",
                            "top": "0",
                            "left": "0",
                            "overflow": "hidden",
                            "backgroundImage": "linear-gradient(to right, rgba(127,144,148,1), rgba(145,191,202,1))",
                            "display": "flex",
                            "flexDirection": "column",
                            "alignItems": "center",
                            "justifyContent": "center",
                            "color": "#FFFFFF",
                            "borderRadius": 50,
                            "boxShadow": "0 10px 15px rgba(0,0,0,0.3)"
                        }
                    },
                    "id": crypto.randomUUID()
                }
            ],
            "style": {
                "xl": {
                    "width": "100%",
                    "minWidth": "2px",
                    "minHeight": "1vh",
                    "height": "98vh",
                    "paddingTop": 100,
                    "paddingRight": 100,
                    "paddingBottom": 100,
                    "paddingLeft": 100,
                    "marginLeft": "0",
                    "marginRight": "0",
                    "marginTop": "0",
                    "marginBottom": "0",
                    "position": "relative",
                    "top": "0",
                    "left": "0",
                    "overflow": "hidden",
                    "backgroundImage": "linear-gradient(to right, rgba(178,174,174,1), rgba(182,182,195,1))"
                },
                "lg": {
                    "width": "100%",
                    "minWidth": "2px",
                    "minHeight": "1vh",
                    "height": "98vh",
                    "paddingTop": 100,
                    "paddingRight": 100,
                    "paddingBottom": 100,
                    "paddingLeft": 100,
                    "marginLeft": "0",
                    "marginRight": "0",
                    "marginTop": "0",
                    "marginBottom": "0",
                    "position": "relative",
                    "top": "0",
                    "left": "0",
                    "overflow": "hidden",
                    "backgroundImage": "linear-gradient(to right, rgba(178,174,174,1), rgba(182,182,195,1))"
                },
                "md": {
                    "width": "100%",
                    "minWidth": "2px",
                    "minHeight": "1vh",
                    "height": "98vh",
                    "paddingTop": 50,
                    "paddingRight": 50,
                    "paddingBottom": 50,
                    "paddingLeft": 50,
                    "marginLeft": "0",
                    "marginRight": "0",
                    "marginTop": "0",
                    "marginBottom": "0",
                    "position": "relative",
                    "top": "0",
                    "left": "0",
                    "overflow": "hidden",
                    "backgroundImage": "linear-gradient(to right, rgba(178,174,174,1), rgba(182,182,195,1))"
                },
                "sm": {
                    "width": "100%",
                    "minWidth": "2px",
                    "minHeight": "1vh",
                    "height": "98vh",
                    "paddingTop": 20,
                    "paddingRight": 20,
                    "paddingBottom": 20,
                    "paddingLeft": 20,
                    "marginLeft": "0",
                    "marginRight": "0",
                    "marginTop": "0",
                    "marginBottom": "0",
                    "position": "relative",
                    "top": "0",
                    "left": "0",
                    "overflow": "hidden",
                    "backgroundImage": "linear-gradient(to right, rgba(178,174,174,1), rgba(182,182,195,1))"
                }
            },
            "id": crypto.randomUUID()
        }
    ],
    "createdAt": "",
    "updatedAt": ""
}