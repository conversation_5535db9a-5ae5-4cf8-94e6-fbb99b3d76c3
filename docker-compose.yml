services:
  # kama-postgres:
  #   image: postgres:latest
  #   container_name: kama-postgres
  #   # restart: always
  #   env_file:
  #     - .env # Dynamically load the correct .env file
  #   ports:
  #     - "${POSTGRES_PORT}:5432" # Expose for development only
  #   networks:
  #     - cms_network
  #   environment:
  #     POSTGRES_USER: ${POSTGRES_USER}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  #     POSTGRES_DB: ${POSTGRES_DB}
  #     POSTGRES_LISTEN_ADDRESS: 0.0.0.0
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data # Persist database data

  kama-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        DATABASE_URL: ${DATABASE_URL}
    container_name: kama-backend
    env_file:
      - .env # Dynamically load the correct .env file
    ports:
      - "${BACKEND_PORT}:3000" # Expose for development only
    networks:
      - cms_network
    # depends_on:
    #   - postgres
    volumes:
      - ./backend:/app # Mount the source code for live updates
      - backend-data:/app/data # Persist backend data
      - /app/node_modules # Avoid overwriting node_modules in the container
    environment:
      - CHOKIDAR_USEPOLLING=true # Enables file watching inside Docker


networks:
  cms_network:
    driver: bridge

volumes:
  # postgres-data:
  backend-data:
